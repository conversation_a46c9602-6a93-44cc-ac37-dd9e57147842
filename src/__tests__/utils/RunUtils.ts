import {Job} from '../../runs/jobs/Job';
import PriorityQueue from '../../runs/PriorityQueue';
import {Run} from '../../runs/Run';
import {RunManager} from '../../runs/RunManager';
import {TriggeredBy} from '../../telemetry/scraperService/scraperServiceEvents';
import {Command, Source} from '../../types';
import {Deferred} from '../../utils/Deferred';
import {shadowModeScrapeTestTask} from './entries';
import {asyncVoidFunc} from './helpers';
import {TestJob} from './TestJob';

export class TestRun extends Run {
    constructor(jobs: Job[], options: {priority?: number; source?: Source; isShadowRun?: boolean; forceJobRun?: boolean} = {}) {
        super(jobs, {
            priority: options.priority ?? 0,
            context: {
                source: options.source ?? Source.PLAYSTATION_SALES,
                command: Command.SCRAPE,
                shadowModeTask: options.isShadowRun ? shadowModeScrapeTestTask : undefined,
                forceRunStart: options.forceJobRun ?? false,
                userId: 'o-U53r1D',
                triggeredBy: TriggeredBy.USER_VIA_ELECTRON
            }
        });
    }
}

export async function testWithRunManagerContext(
    functionToTest: (runManagerInTest: RunManager) => Promise<void>,
    runManagerWorkerPoolSize = 1,
    queUpdateInterval = 1
): Promise<void> {
    const runManager = createRunManager(runManagerWorkerPoolSize, queUpdateInterval);
    try {
        return await functionToTest(runManager);
    } finally {
        await runManager.close();
    }
}

export const createRun = (jobs: Job[], options: {priority?: number; source?: Source; isShadowRun?: boolean; forceJobRun?: boolean} = {}) => {
    return new TestRun(jobs, {
        priority: options.priority ?? 0,
        source: options.source ?? Source.PLAYSTATION_SALES,
        isShadowRun: options.isShadowRun ?? false,
        forceJobRun: options.forceJobRun ?? false
    });
};
export const createRunManager = (limit: number, queUpdateInterval = 1) => {
    const que = new PriorityQueue(limit, queUpdateInterval);
    const runManager = new RunManager(asyncVoidFunc, que);
    runManager.init();
    return runManager;
};

export function getJobAndItsDeferredPromise(jobName?: string) {
    const deferred = new Deferred();
    const job = new TestJob(async () => {
        await deferred.promise;
    }, jobName);
    deferred.promise.catch(console.info);

    return {deferred, job};
}

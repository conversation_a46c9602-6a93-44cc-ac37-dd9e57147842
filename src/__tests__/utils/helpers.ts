/* istanbul ignore file */
import * as fs from 'fs';
import * as path from 'path';
import * as _ from 'lodash';
import {v4 as createUUID} from 'uuid';
import {DEFAULT_API_URL, loginToApiWithCredentials, setupApi} from '../../api/ScraperApi';
import {SourceAccountAddParams} from '../../configurations/SourceAccount';
import {DependenciesManager} from '../../dependencies/DependenciesManager';
import {ScraperLibEmitter} from '../../emitter';
import {BinaryResult} from '../../processes/types';
import {Execution} from '../../processes/types/Execution';
import {SourceCredentials} from '../../runs/credentials';
import PriorityQueue from '../../runs/PriorityQueue';
import {ScraperLib} from '../../ScraperLib';
import {ElectronStorageAdapter} from '../../storage/ElectronStorageImplementation';
import {iScraperServiceClient} from '../../telemetry/scraperService/iScraperServiceClient';
import {Source} from '../../types';
import {Deferred} from '../../utils/Deferred';
import {getMockS2Client} from './s2Mocks';

const {INDIEBIE_LOGIN, INDIEBIE_PASSWORD} = process.env;

export const asyncVoidFunc = async () => {
    /* void */
};
export const scraperLibMainDirectory = '.private';

export function newAdapterWithStorage(initialMemoryState?: any): {adapter: ElectronStorageAdapter; memory: any} {
    const memory = _.cloneDeep(initialMemoryState || {});
    return {
        adapter: {
            set: async <T>(key: string, value: T) => {
                _.set(memory, key, value);
            },
            get: async <T>(key: string): Promise<T> => {
                return _.get(memory, key) as T;
            },
            delete: async (key: string) => {
                _.unset(memory, key);
            }
        },
        memory
    };
}

export function newAdapterWithFileStorage(mail: string = INDIEBIE_LOGIN!, filePath = path.join(scraperLibMainDirectory, 'Store.json')): ElectronStorageAdapter {
    const fullKey = (key: string): string[] => ['accountData', mail, 'scrapers', ...key.split('.')]; // This format should be compatible with decrypted electron store
    const readFile = async (): Promise<any> => {
        try {
            if (!fs.existsSync(filePath)) {
                return {};
            }
            const data = await fs.promises.readFile(filePath, 'utf-8');
            return data ? JSON.parse(data) : {};
            // eslint-disable-next-line @typescript-eslint/no-unused-vars
        } catch (error) {
            return {};
        }
    };

    const writeFile = async (data: any): Promise<void> => {
        await fs.promises.writeFile(filePath, JSON.stringify(data, null, 2), 'utf-8');
    };

    return {
        set: async <T>(key: string, value: T) => {
            const data = await readFile();
            _.set(data, fullKey(key), value);
            await writeFile(data);
        },
        get: async <T>(key: string): Promise<T> => {
            const data = await readFile();
            return _.get(data, fullKey(key)) as T;
        },
        delete: async (key: string) => {
            const data = await readFile();
            _.unsent(data, fullKey(key));
            await writeFile(data);
        }
    };
}

/* eslint-disable @typescript-eslint/no-empty-function */
export const mockScraperServiceClient: iScraperServiceClient = getMockS2Client();

class MockPriorityQueue extends PriorityQueue {
    public startInterval() {}
}
/* eslint-enable @typescript-eslint/no-empty-function */

export async function getFreshLib({
    emitter,
    shouldLogin,
    skipInit,
    shadowMode,
    storageAdapter,
    sentryDSN,
    withMockPriorityQueue,
    persistStorage
}: {
    emitter?: ScraperLibEmitter;
    shouldLogin?: boolean;
    skipInit?: boolean;
    shadowMode?: {active: boolean; interval: number};
    storageAdapter?: ElectronStorageAdapter;
    sentryDSN?: string;
    withMockPriorityQueue?: boolean;
    persistStorage?: boolean;
} = {}): Promise<ScraperLib> {
    let apiToken = 'FAKE_TOKEN';
    if (shouldLogin) {
        setupApi();
        apiToken = await loginToApiWithCredentials(INDIEBIE_LOGIN!, INDIEBIE_PASSWORD!);
    }

    const lib = new ScraperLib({
        mainDir: scraperLibMainDirectory,
        emitter,
        userId: 'u-fake-user-id',
        apiUrl: DEFAULT_API_URL,
        scraperServiceUrl: 'https://scraper-service.indiebi.dev',
        apiToken,
        storageOptions: {type: 'json', adapter: storageAdapter ?? (persistStorage ? newAdapterWithFileStorage() : newAdapterWithStorage().adapter)},
        sentryDSN,
        scraperServiceClient: shouldLogin ? undefined : mockScraperServiceClient,
        runManagerPriorityQue: withMockPriorityQueue ? new MockPriorityQueue(1) : undefined
    });

    if (!skipInit) {
        await lib.init({
            syncBinsOnInit: false,
            shadowMode: shadowMode || {active: false, interval: 0},
            loadOutboxDumpFile: false
        });
    }
    return lib;
}

export function getFreshDependenciesManager(): DependenciesManager {
    return new DependenciesManager(scraperLibMainDirectory);
}

const sessionDirectory = path.join(scraperLibMainDirectory, 'sessions');

export const testSourceAccountWithParams = (params?: Record<string, any>, sessionFileName = 'file'): SourceAccountAddParams => ({
    accountIdentifier: 'test',
    sessionPath: path.join(sessionDirectory, `${sessionFileName}.json`),
    cliParams: params ? {...params} : undefined
});

export const processResultMock =
    <T extends BinaryResult>(result: T) =>
    (): Promise<Execution<T>> => {
        const deferred = new Deferred<Execution<T>>();
        const deferredResult = new Deferred<T>();
        const execution = new Execution<T>(1, deferredResult.promise);

        jest.spyOn(Execution.prototype, 'kill').mockImplementation(async () => {
            deferredResult.resolve(result);
        });

        deferred.resolve(execution);

        setImmediate(() => {
            deferredResult.resolve(result);
        });

        return deferred.promise;
    };

export const processRejectMock = (rejectWith: any) => (): Promise<Execution<any>> => {
    const deferred = new Deferred<Execution<any>>();
    const deferredResult = new Deferred<any>();
    const execution = new Execution<any>(1, deferredResult.promise);

    // Mock the kill method to handle the rejection properly
    jest.spyOn(Execution.prototype, 'kill').mockImplementation(async () => {
        // We don't need to reject again if it's already rejected,
        // but we need to ensure the promise is handled
        try {
            await deferredResult.promise.catch((e) => {
                console.log(e);
            });
        } catch {
            // Ignore errors
        }
    });

    // Add a cleanup function to the execution object
    const originalCatch = execution.result.catch.bind(execution.result);
    execution.result.catch = function (...args) {
        // When the result promise is rejected, ensure it's handled
        return originalCatch(...args);
    };

    deferred.resolve(execution);

    // Use setImmediate for immediate rejection
    setImmediate(() => {
        deferredResult.reject(rejectWith);
    });

    return deferred.promise;
};

export function getCredentials(source: Source): SourceCredentials[Source] {
    let credentials: SourceCredentials[Source];
    if (source === Source.PLAYSTATION_SALES) {
        credentials = {
            clientId: process.env.PLAYSTATION_SALES_CLIENT_ID || '',
            clientSecret: process.env.PLAYSTATION_SALES_CLIENT_SECRET || ''
        };
    } else if (source === Source.MICROSOFT_SALES) {
        credentials = {
            apiSetupData: JSON.parse(process.env.MICROSOFT_SALES_API_SETUP_DATA || '[]')
        };
    } else {
        credentials = {
            user: process.env[`${source.toUpperCase()}_USER`] || '',
            password: process.env[`${source.toUpperCase()}_PASSWORD`] || ''
        };
    }
    return credentials;
}

export const generateRandomAuthCode = () => createUUID().split('-')[0].toUpperCase();

export function testValidationIsCalledWhenEntryIsCreated(EntryClass, validJson) {
    const title = `static ${EntryClass.name}.create method calls validate() on instance created inside`;

    it(title, async () => {
        const validateSpy = jest.spyOn(EntryClass.prototype, 'validate');
        const entry = await EntryClass.create(validJson);

        expect(validateSpy).toHaveBeenCalled();
        expect(entry).toBeInstanceOf(EntryClass);
        expect(entry.type).toBe(validJson.type);
    });
}

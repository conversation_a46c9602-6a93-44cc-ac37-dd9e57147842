import {errorType} from '../../../configurations/errorType';
import {ScraperConfiguration, ScraperConfigurationStatus} from '../../../configurations/ScraperConfiguration';
import {convertToPublicScraperConfiguration} from '../../../configurations/ScraperConfigurationUtils';
import {PublicScraperConfiguration} from '../../../publicInterfaces/PublicScraperConfiguration';
import {ScrapeRun} from '../../../runs/ScrapeRun';
import {Source} from '../../../types';
import {Deferred} from '../../../utils/Deferred';
import {createDateRange} from '../../utils/datesHelpers';
import {createRun, testWithRunManagerContext} from '../../utils/RunUtils';
import {TestJob} from '../../utils/TestJob';

describe('convertToPublicScraperConfiguration should', () => {
    const mockConfig: ScraperConfiguration = {
        id: 'test-config-id',
        source: Source.NINTENDO_SALES,
        status: ScraperConfigurationStatus.CONFIGURED,
        errorType: errorType.UNEXPECTED_ERROR,
        previousStatus: ScraperConfigurationStatus.ERROR,
        previousErrorType: errorType.DEPENDENCIES_SYNC_ERROR,
        sourceAccountId: 'test-account-id',
        lastSuccessfulScrapeDate: new Date('2023-01-01'),
        createdAt: new Date('2022-12-01')
    };

    it('convert configuration with no active runs', async () => {
        await testWithRunManagerContext(async (runManager) => {
            const result = convertToPublicScraperConfiguration(mockConfig, runManager);

            expect(result).toEqual({
                id: mockConfig.id,
                source: mockConfig.source,
                activeRun: undefined,
                lastSuccessfulScrapeDate: mockConfig.lastSuccessfulScrapeDate,
                status: ScraperConfigurationStatus.CONFIGURED,
                errorType: mockConfig.errorType,
                previousStatus: mockConfig.previousStatus,
                previousErrorType: mockConfig.previousErrorType,
                sourceAccountId: mockConfig.sourceAccountId,
                createdAt: mockConfig.createdAt
            });
        });
    });

    it('convert configuration with running run', async () => {
        await testWithRunManagerContext(async (runManager) => {
            const job = new TestJob(async () => {
                await new Deferred().promise;
            });

            const run = createRun([job], {source: Source.NINTENDO_SALES, isShadowRun: false});

            // Mock the progress and startDate properties
            Object.defineProperty(run, 'progress', {
                value: 25,
                writable: false,
                configurable: true
            });
            Object.defineProperty(run, 'startDate', {
                value: new Date('2023-01-15'),
                writable: false,
                configurable: true
            });

            runManager.addRun(run).catch(void 0);

            const result = convertToPublicScraperConfiguration(mockConfig, runManager);

            expect(result.status).toEqual(ScraperConfigurationStatus.RUNNING_SCRAPE);
            expect(result.activeRun).toEqual({
                percentageProgress: 25,
                startDate: new Date('2023-01-15')
            });
        });
    });

    it('convert configuration with scheduled run', async () => {
        await testWithRunManagerContext(async (runManager) => {
            const scheduledConfig = {...mockConfig, source: Source.STEAM_SALES};

            const job = new TestJob(async () => {
                await new Deferred().promise;
            });

            const run = createRun([job], {source: Source.STEAM_SALES, isShadowRun: false});
            run.progress = 0;
            run.startDate = new Date('2023-01-20');

            runManager.addRun(run).catch(void 0);

            const result = convertToPublicScraperConfiguration(scheduledConfig, runManager);

            expect(result.status).toEqual(ScraperConfigurationStatus.SCHEDULED);
            expect(result.activeRun).toEqual({
                percentageProgress: 0,
                startDate: new Date('2023-01-20')
            });
        });
    });

    it('convert configuration with ScrapeRun should include scrape-specific fields', async () => {
        await testWithRunManagerContext(async (runManager) => {
            // Create a mock ScrapeRun
            const mockScrapeRun = {
                progress: 50,
                startDate: new Date('2023-01-25'),
                dateRanges: [createDateRange('2023-01-01', '2023-01-07'), createDateRange('2023-01-08', '2023-01-14')],
                totalItems: 100,
                processedItems: 50,
                context: {
                    source: Source.NINTENDO_SALES,
                    isShadowRun: false
                },
                isActive: () => true
            } as unknown as ScrapeRun;

            // Mock the runManager methods to return our mock ScrapeRun
            jest.spyOn(runManager, 'getRunningRunForSource').mockReturnValue(mockScrapeRun);
            jest.spyOn(runManager, 'getScheduledRunForSource').mockReturnValue(undefined);

            const result = convertToPublicScraperConfiguration(mockConfig, runManager);

            expect(result.status).toEqual(ScraperConfigurationStatus.RUNNING_SCRAPE);
            expect(result.activeRun).toEqual({
                percentageProgress: 50,
                startDate: new Date('2023-01-25'),
                numOfDataRangesToScrape: 2,
                numberOfDaysToScrape: 100,
                scrapedDays: 50
            });
        });
    });

    it('prioritize running run over scheduled run', async () => {
        await testWithRunManagerContext(async (runManager) => {
            const job = new TestJob(async () => {
                await new Deferred().promise;
            });

            const runningRun = createRun([job], {source: Source.NINTENDO_SALES, isShadowRun: false});
            runningRun.progress = 75;
            runningRun.startDate = new Date('2023-01-30');

            const scheduledRun = createRun([job], {source: Source.NINTENDO_SALES, isShadowRun: false});
            scheduledRun.progress = 0;
            scheduledRun.startDate = new Date('2023-02-01');

            // Mock both runs existing
            jest.spyOn(runManager, 'getRunningRunForSource').mockReturnValue(runningRun);
            jest.spyOn(runManager, 'getScheduledRunForSource').mockReturnValue(scheduledRun);

            const result = convertToPublicScraperConfiguration(mockConfig, runManager);

            expect(result.status).toEqual(ScraperConfigurationStatus.RUNNING_SCRAPE);
            expect(result.activeRun).toEqual({
                percentageProgress: 75,
                startDate: new Date('2023-01-30')
            });
        });
    });

    it('return serializable result', async () => {
        await testWithRunManagerContext(async (runManager) => {
            const result = convertToPublicScraperConfiguration(mockConfig, runManager);

            // Test that the result can be serialized to JSON and back
            const serialized = JSON.stringify(result);
            const deserialized = JSON.parse(serialized);

            expect(deserialized).toBeDefined();
            expect(deserialized.id).toBe(mockConfig.id);
            expect(deserialized.source).toBe(mockConfig.source);
        });
    });
});

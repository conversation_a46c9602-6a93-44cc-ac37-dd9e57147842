import {BinaryProxy, getResultClass} from '../../../dependencies/BinaryProxy';
import {findParamValue} from '../../../processes/spawnProcess';
import * as process from '../../../processes/spawnProcess';
import {ManualLoginDetailsResult} from '../../../processes/types';
import {filterSensitiveData} from '../../../telemetry/filterSensitiveData';
import {Command, Source} from '../../../types';
import {asyncVoidFunc, getFreshDependenciesManager, processResultMock, scraperLibMainDirectory} from '../../utils/helpers';

let proxy: BinaryProxy;
const spawnProcessSpy = jest.spyOn(process, 'spawnProcess');

// minimal length of array returned by `parseParams` method
const MINIMAL_NUMBER_OF_PARAMS = 6;
const MINIMAL_NUMBER_OF_PARAMS_FOR_SCRAPE = MINIMAL_NUMBER_OF_PARAMS + 1;

const generateProxyRunTestAndGetParamsArray = async (command: Command, params: any, minExpectedParamsLength: number): Promise<readonly string[]> => {
    spawnProcessSpy.mockImplementation(processResultMock(null));
    const exec = await proxy.run(command, params, 'fake', 'o-U53r1D');
    expect(spawnProcessSpy).toBeCalledTimes(1);
    const [_, parsedParams] = spawnProcessSpy.mock.calls[0];
    expect(parsedParams[0]).toBe(command);
    expect(parsedParams.length).toBeGreaterThanOrEqual(minExpectedParamsLength);
    expect(await exec.result).toBe(null);
    expect(findParamValue('--source', parsedParams)).toBe(params.source);
    return parsedParams;
};

const mockedBinProxy = (): BinaryProxy => {
    const manager = getFreshDependenciesManager();
    jest.spyOn(manager, 'downloadDependency').mockImplementation(asyncVoidFunc);
    jest.spyOn(manager, 'syncDependencies').mockImplementation(asyncVoidFunc);
    jest.spyOn(manager, 'getDependencyExecPaths').mockResolvedValue({chromiumExecPath: 'chromium.exe', scrapersExecPath: 'scrapers.exe'});
    return new BinaryProxy(scraperLibMainDirectory, manager);
};

describe('Binaries Proxy', () => {
    beforeEach(() => {
        jest.resetAllMocks();
        proxy = mockedBinProxy();
    });

    afterEach(async () => {
        jest.resetAllMocks();
    });

    test('should correctly map params for login command', async () => {
        const credentials = {user: 'x', password: 'y'};

        const parsedParams = await generateProxyRunTestAndGetParamsArray(
            Command.LOGIN,
            {
                source: Source.EPIC_SALES,
                credentials
            },
            MINIMAL_NUMBER_OF_PARAMS
        );

        expect(findParamValue('--reportPath', parsedParams)).not.toBeDefined();
        const credentialsString = findParamValue('--credentials', parsedParams) ?? '';
        expect(JSON.parse(credentialsString)).toEqual(credentials);
    });

    test('should correctly map params for scrape command', async () => {
        const skuToIgnore = ['1', '2'];
        const dateFrom = new Date();
        const dateTo = new Date();

        const parsedParams = await generateProxyRunTestAndGetParamsArray(
            Command.SCRAPE,
            {source: Source.EPIC_SALES, skuToIgnore, dateFrom, dateTo},
            MINIMAL_NUMBER_OF_PARAMS_FOR_SCRAPE
        );

        expect(findParamValue('--reportPath', parsedParams)).toBeDefined();
        expect(findParamValue('--from', parsedParams)).toBeDefined();
        expect(findParamValue('--to', parsedParams)).toBeDefined();
        const excludedSkusString = findParamValue('--excludedSkus', parsedParams) ?? '';
        expect(JSON.parse(excludedSkusString)).toEqual(skuToIgnore);
    });
});

describe('filterSensitiveParams', () => {
    const params = [
        'login',
        '--source=microsoft_sales',
        '--output=json',
        '--apiUrl=https://scraper-api.indiebi.com',
        '--apiToken=xyz',
        "--chromePath='chrome.exe'",
        "--sessionFile='session.json'",
        '--credentials={"user":"user","password":"password"}'
    ];

    test('should filter sensitive params', () => {
        const filteredParams = filterSensitiveData(params);
        expect(filteredParams.length).toBe(6);
        expect(filteredParams.some((param) => param.startsWith('--credentials'))).toBe(false);
        expect(filteredParams.some((param) => param.startsWith('--apiToken'))).toBe(false);
    });
});

describe('getResultClass', () => {
    test('should correctly return default class', () => {
        const result = getResultClass(Command.GET_MANUAL_LOGIN_DETAILS, Source.EPIC_SALES);
        expect(result).toBe(ManualLoginDetailsResult);
    });

    test('should correctly return class with source override', () => {
        const result = getResultClass(Command.GET_MANUAL_LOGIN_DETAILS, Source.PLAYSTATION_SALES);
        expect(result).toBe(null);
    });
});

import {errorType} from '../../../configurations/errorType';
import {BinaryProxy, LoginParams} from '../../../dependencies/BinaryProxy';
import {DependenciesManager} from '../../../dependencies/DependenciesManager';
import {LoginRunContext} from '../../../runs/jobs/context';
import {LoginRun} from '../../../runs/LoginRun';
import {TriggeredBy} from '../../../telemetry/scraperService/scraperServiceEvents';
import {Source} from '../../../types';
import * as fileUtils from '../../../utils/fileUtils';
import {shadowModeLoginTestTask} from '../../utils/entries';
import {mockScraperServiceClient} from '../../utils/helpers';

const sessionPath = 'fakePath';
let sendLoginStateChangedEventSpy: jest.SpyInstance;

function getLoginRunMocks(context: Partial<LoginRunContext> = {}) {
    return new LoginRun(
        {
            dependenciesManager: {} as DependenciesManager,
            binaryProxy: {} as BinaryProxy,
            s2Client: mockScraperServiceClient,
            loginParams: {sessionPath: sessionPath} as LoginParams
        },
        {
            context: {
                ...(context as LoginRunContext)
            }
        }
    );
}

describe('ScrapeRun', () => {
    beforeEach(async () => {
        sendLoginStateChangedEventSpy = jest.spyOn(mockScraperServiceClient, 'scheduleLoginStateChangedEvent');
    });
    afterEach(jest.restoreAllMocks);

    it('LoginRun.onScheduled should send SCHEDULED event to S2', async () => {
        const run = getLoginRunMocks({source: Source.STEAM_SALES, triggeredBy: TriggeredBy.USER_VIA_ELECTRON});

        await run.onScheduled!();
        expect(sendLoginStateChangedEventSpy).toBeCalledWith({
            source: Source.STEAM_SALES,
            newState: 'SCHEDULED',
            isManualSession: false,
            triggeredBy: TriggeredBy.USER_VIA_ELECTRON,
            operationId: expect.anything(),
            accountIdentifier: undefined
        });
    });

    it('LoginRun.onStart should send STARTED event to S2', async () => {
        const run = getLoginRunMocks({source: Source.STEAM_SALES, triggeredBy: TriggeredBy.USER_VIA_ELECTRON});

        await run.onStart!();
        expect(sendLoginStateChangedEventSpy).toBeCalledWith({
            source: Source.STEAM_SALES,
            newState: 'STARTED',
            isManualSession: false,
            triggeredBy: TriggeredBy.USER_VIA_ELECTRON,
            operationId: expect.anything(),
            accountIdentifier: undefined
        });
    });

    it('LoginRun.onFail should send FAILED event to S2 with errorType', async () => {
        const run = getLoginRunMocks({source: Source.STEAM_SALES, triggeredBy: TriggeredBy.USER_VIA_ELECTRON});
        const error = {errorType: errorType.DEPENDENCIES_SYNC_ERROR};

        await run.onFail!(error);
        expect(sendLoginStateChangedEventSpy).toBeCalledWith({
            source: Source.STEAM_SALES,
            newState: 'FAILED',
            isManualSession: false,
            triggeredBy: TriggeredBy.USER_VIA_ELECTRON,
            operationId: expect.anything(),
            accountIdentifier: undefined,
            reason: errorType.DEPENDENCIES_SYNC_ERROR
        });
    });

    it('LoginRun.onFail should send FAILED event to S2 with default errorType if none provided', async () => {
        const run = getLoginRunMocks({source: Source.STEAM_SALES, triggeredBy: TriggeredBy.USER_VIA_ELECTRON});
        const error = {};

        await run.onFail!(error);
        expect(sendLoginStateChangedEventSpy).toBeCalledWith({
            source: Source.STEAM_SALES,
            newState: 'FAILED',
            isManualSession: false,
            triggeredBy: TriggeredBy.USER_VIA_ELECTRON,
            operationId: expect.anything(),
            accountIdentifier: undefined,
            reason: errorType.UNEXPECTED_ERROR
        });
    });

    it('LoginRun.onKill should send STOPPED event to S2', async () => {
        const run = getLoginRunMocks({source: Source.STEAM_SALES, triggeredBy: TriggeredBy.USER_VIA_ELECTRON});

        await run.onKill!();
        expect(sendLoginStateChangedEventSpy).toBeCalledWith({
            source: Source.STEAM_SALES,
            newState: 'STOPPED',
            isManualSession: false,
            triggeredBy: TriggeredBy.USER_VIA_ELECTRON,
            operationId: expect.anything(),
            accountIdentifier: undefined
        });
    });

    it('LoginRun.onFinally should remove sessionPath if isShadowRun is true', async () => {
        const removeFileOrDirectorySpy = jest.spyOn(fileUtils, 'removeFileOrDirectory');
        const run = getLoginRunMocks({shadowModeTask: shadowModeLoginTestTask, source: Source.STEAM_SALES, triggeredBy: TriggeredBy.USER_VIA_ELECTRON});

        await run.onFinally!();
        expect(removeFileOrDirectorySpy).toBeCalledWith(sessionPath, false);
    });

    it('LoginRun.onFinally should not remove sessionPath if isShadowRun is false', async () => {
        const removeFileOrDirectorySpy = jest.spyOn(fileUtils, 'removeFileOrDirectory');
        const run = getLoginRunMocks({source: Source.STEAM_SALES, triggeredBy: TriggeredBy.USER_VIA_ELECTRON});

        await run.onFinally!();
        expect(removeFileOrDirectorySpy).not.toBeCalled();
    });
});

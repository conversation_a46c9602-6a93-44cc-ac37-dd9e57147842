import {errorType} from '../../../../configurations/errorType';
import {ScraperConfiguration, ScraperConfigurationStatus} from '../../../../configurations/ScraperConfiguration';
import {ScraperConfigurationHandlerWizardUtil} from '../../../../configurations/ScraperConfigurationHandlerWizardUtil';
import {CheckSessionResult} from '../../../../processes/types';
import {ScraperLibError} from '../../../../processes/types/errors';
import {LoginRunContext} from '../../../../runs/jobs/context';
import {SaveConfigurationToStorageJob} from '../../../../runs/jobs/SaveConfigurationToStorageJob';
import {TriggeredBy} from '../../../../telemetry/scraperService/scraperServiceEvents';
import {Source} from '../../../../types';

const createMockConfigurations = (): ScraperConfiguration[] => [
    {
        id: 'config-1',
        source: Source.STEAM_SALES,
        sourceAccountId: 'account-1',
        status: ScraperConfigurationStatus.CONFIGURED,
        createdAt: new Date()
    },
    {
        id: 'config-2',
        source: Source.STEAM_WISHLISTS,
        sourceAccountId: 'account-1',
        status: ScraperConfigurationStatus.CONFIGURED,
        createdAt: new Date()
    }
];

const createMockContext = (overrides: Partial<LoginRunContext> = {}): LoginRunContext => ({
    source: Source.STEAM_SALES,
    sessionPath: '/path/to/session',
    operationId: 'test-operation-id',
    userId: 'test-user-id',
    command: 'LOGIN' as any,
    isShadowRun: false,
    forceRunStart: false,
    triggeredBy: TriggeredBy.USER_VIA_ELECTRON,
    mutable: {
        checkSessionResult: {
            id: 'test-account-id',
            hasScrapeBlockingIssues: false
        } as CheckSessionResult
    },
    ...overrides
});

const createMockWizardUtil = (mockConfigurations: ScraperConfiguration[] = createMockConfigurations()): jest.Mocked<ScraperConfigurationHandlerWizardUtil> =>
    ({
        addSourceAccountAndHandleRelatedConfigs: jest.fn().mockResolvedValue(mockConfigurations)
    } as any);

describe('SaveConfigurationToStorageJob', () => {
    afterEach(() => {
        jest.restoreAllMocks();
    });

    describe('execute', () => {
        it('should successfully save configuration when session is valid', async () => {
            // Given
            const mockConfigurations = createMockConfigurations();
            const mockWizardUtil = createMockWizardUtil(mockConfigurations);
            const job = new SaveConfigurationToStorageJob(mockWizardUtil);
            const context = createMockContext();

            // When
            const result = await job.execute(context);

            // Then
            expect(mockWizardUtil.addSourceAccountAndHandleRelatedConfigs).toHaveBeenCalledWith(Source.STEAM_SALES, {
                sessionPath: '/path/to/session',
                accountIdentifier: 'test-account-id'
            });
            expect(result).toEqual(mockConfigurations);
        });

        const invalidCheckSessionResultCases = [
            {
                description: 'when checkSessionResult is undefined',
                checkSessionResult: undefined
            },
            {
                description: 'when checkSessionResult is null',
                checkSessionResult: null
            },
            {
                description: 'when checkSessionResult.id is undefined',
                checkSessionResult: {
                    id: undefined,
                    hasScrapeBlockingIssues: false
                }
            },
            {
                description: 'when checkSessionResult.id is null',
                checkSessionResult: {
                    id: null,
                    hasScrapeBlockingIssues: false
                }
            },
            {
                description: 'when checkSessionResult.id is empty string',
                checkSessionResult: {
                    id: '',
                    hasScrapeBlockingIssues: false
                }
            }
        ];

        test.each(invalidCheckSessionResultCases)('should throw ScraperLibError $description', async ({checkSessionResult}) => {
            // Given
            const mockWizardUtil = createMockWizardUtil();
            const job = new SaveConfigurationToStorageJob(mockWizardUtil);
            const context = createMockContext({
                mutable: {
                    checkSessionResult: checkSessionResult as any
                }
            });

            // When & Then
            await expect(job.execute(context)).rejects.toThrow(ScraperLibError);
            await expect(job.execute(context)).rejects.toThrow(errorType.INTERNAL_SCRAPER_LIB_ERROR);
        });

        it('should include context in error when checkSessionResult is invalid', async () => {
            // Given
            const mockWizardUtil = createMockWizardUtil();
            const job = new SaveConfigurationToStorageJob(mockWizardUtil);
            const context = createMockContext({
                mutable: {
                    checkSessionResult: undefined
                }
            });

            // When & Then
            try {
                await job.execute(context);
                fail('Expected error to be thrown');
            } catch (error) {
                expect(error).toBeInstanceOf(ScraperLibError);
                expect((error as ScraperLibError).additionalErrorData?.message).toBe('CheckSessionResult is not defined and a configuration is trying to be saved');
                expect((error as ScraperLibError).additionalErrorData?.context).toEqual(context);
            }
        });

        it('should propagate errors from addSourceAccountAndHandleRelatedConfigs', async () => {
            // Given
            const error = new Error('Storage error');
            const mockWizardUtil = createMockWizardUtil();
            mockWizardUtil.addSourceAccountAndHandleRelatedConfigs.mockRejectedValue(error);
            const job = new SaveConfigurationToStorageJob(mockWizardUtil);
            const context = createMockContext();

            // When & Then
            await expect(job.execute(context)).rejects.toThrow('Storage error');
        });
    });

    it('should complete onSuccess without error', async () => {
        // Given
        const mockWizardUtil = createMockWizardUtil();
        const job = new SaveConfigurationToStorageJob(mockWizardUtil);

        // When & Then
        await expect(job.onSuccess()).resolves.toBeUndefined();
    });

    it('should complete onFail without error', async () => {
        // Given
        const mockWizardUtil = createMockWizardUtil();
        const job = new SaveConfigurationToStorageJob(mockWizardUtil);
        const error = new Error('Test error');

        // When & Then
        await expect(job.onFail(error)).resolves.toBeUndefined();
    });

    it('should complete kill without error', async () => {
        // Given
        const mockWizardUtil = createMockWizardUtil();
        const job = new SaveConfigurationToStorageJob(mockWizardUtil);

        // When & Then
        await expect(job.kill()).resolves.toBeUndefined();
    });

    it('should create instance with provided dependencies', () => {
        // Given
        const mockWizardUtil = createMockWizardUtil();

        // When
        const job = new SaveConfigurationToStorageJob(mockWizardUtil);

        // Then
        expect(job).toBeInstanceOf(SaveConfigurationToStorageJob);
    });
});

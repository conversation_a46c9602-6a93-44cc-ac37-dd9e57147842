import {ScrapeResult} from '../../../../processes/types';
import {ScrapeRunContext} from '../../../../runs/jobs/context';
import {UploadReportJob} from '../../../../runs/jobs/UploadReportJob';
import {Source} from '../../../../types';
import {mockScraperServiceClient} from '../../../utils/helpers';
import {mockReportInfo, mockReportUpload} from '../../../utils/scraperRunMocks';

describe('UploadReportJob', () => {
    let uploadReportSpy: jest.SpyInstance;
    let sendReportUploadInfoSpy: jest.SpyInstance;
    let uploadReportJob: UploadReportJob;

    const latestScrapeResults = [
        {
            startDate: '2020-01-01',
            endDate: '2020-01-08',
            source: Source.STEAM_SALES,
            reportFileName: 'report1.csv',
            platform: 'pc',
            subplatform: 'steam',
            noData: false
        } as ScrapeResult,
        {
            startDate: '2020-01-08',
            endDate: '2020-01-15',
            source: Source.STEAM_SALES,
            reportFileName: 'report2.csv',
            platform: 'pc',
            subplatform: 'steam',
            noData: false
        } as ScrapeResult
    ];

    beforeEach(() => {
        ({uploadReportSpy, sendReportUploadInfoSpy} = mockReportUpload());
        uploadReportJob = new UploadReportJob('mainDir', mockScraperServiceClient);
    });

    afterEach(() => {
        jest.restoreAllMocks();
    });

    test('UploadReportJob calls uploadReport for each scrape result and sendReportUploadInfo for non-shadow runs', async () => {
        const context = {latestScrapeResults} as unknown as ScrapeRunContext;
        await uploadReportJob.execute(context);

        expect(uploadReportSpy).toHaveBeenCalledTimes(2);
        expect(uploadReportSpy).toHaveBeenCalledWith('mainDir', latestScrapeResults[0]);
        expect(uploadReportSpy).toHaveBeenCalledWith('mainDir', latestScrapeResults[1]);

        expect(sendReportUploadInfoSpy).toHaveBeenCalledTimes(2);
    });

    test('UploadReportJob skips sendReportUploadInfo in shadow run mode', async () => {
        const context = {latestScrapeResults, isShadowRun: true} as unknown as ScrapeRunContext;
        await uploadReportJob.execute(context);

        expect(uploadReportSpy).toHaveBeenCalledTimes(2);
        expect(uploadReportSpy).toHaveBeenCalledWith('mainDir', latestScrapeResults[0]);
        expect(uploadReportSpy).toHaveBeenCalledWith('mainDir', latestScrapeResults[1]);

        expect(sendReportUploadInfoSpy).not.toHaveBeenCalled();
    });

    test('UploadReportJob works with single scrape result', async () => {
        const singleResult = [{report: 'dummy'} as any as ScrapeResult];
        const context = {latestScrapeResults: singleResult} as unknown as ScrapeRunContext;

        await uploadReportJob.execute(context);

        expect(uploadReportSpy).toHaveBeenCalledTimes(1);
        expect(uploadReportSpy).toHaveBeenCalledWith('mainDir', singleResult[0]);
        expect(sendReportUploadInfoSpy).toHaveBeenCalledTimes(1);
    });

    test('UploadReportJob sends ReportUploadEvent on successful upload', async () => {
        const context = {
            latestScrapeResults,
            isShadowRun: false,
            userId: 'test-user',
            operationId: 'test-operation'
        } as unknown as ScrapeRunContext;

        await uploadReportJob.execute(context);
        await uploadReportJob.onSuccess(context);

        expect(sendReportUploadInfoSpy).toHaveBeenCalledTimes(2);
        expect(mockScraperServiceClient.scheduleReportUploadEvent).toHaveBeenCalledTimes(2);

        const calls = (mockScraperServiceClient.scheduleReportUploadEvent as jest.Mock).mock.calls;

        expect(calls[0][0]).toEqual({
            scrapeResult: latestScrapeResults[0],
            reportInfo: mockReportInfo,
            operationId: 'test-operation'
        });
        expect(calls[1][0]).toEqual({
            scrapeResult: latestScrapeResults[1],
            reportInfo: mockReportInfo,
            operationId: 'test-operation'
        });
    });
});

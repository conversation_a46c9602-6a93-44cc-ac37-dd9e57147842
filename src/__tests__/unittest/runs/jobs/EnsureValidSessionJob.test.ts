import {errorType} from '../../../../configurations/errorType';
import {BinaryProxy, CheckSessionParams, CommonParams, LoginParams} from '../../../../dependencies/BinaryProxy';
import {ScraperLibError} from '../../../../processes/types/errors';
import {LoginRunContext} from '../../../../runs/jobs/context';
import {EnsureValidSessionJob} from '../../../../runs/jobs/EnsureValidSessionJob';
import {Command} from '../../../../types';
import {asyncVoidFunc} from '../../../utils/helpers';

describe('EnsureValidSessionJob', () => {
    afterEach(jest.resetAllMocks);

    test('execute checks the session and ends gracefully if it is ok', async () => {
        const binaryProxyMock = {
            run: jest.fn().mockResolvedValue({
                result: Promise.resolve({hasScrapeBlockingIssues: false, id: 'test'})
            })
        } as unknown as BinaryProxy;
        const job = new EnsureValidSessionJob(binaryProxyMock, {} as CheckSessionParams, {} as LoginParams, asyncVoidFunc);
        const context = {operationId: 'fake', userId: 'o-U53r1D', mutable: {}} as LoginRunContext;
        await job.execute(context);
        expect(binaryProxyMock.run).toHaveBeenCalledWith(Command.CHECK_SESSION, {} as CommonParams, context.operationId, context.userId);
    });

    test('execute checks the session but is killed before it ends', async () => {
        const binaryProxyMock = {
            run: jest.fn().mockResolvedValue({
                result: Promise.reject()
            })
        } as unknown as BinaryProxy;
        const job = new EnsureValidSessionJob(binaryProxyMock, {} as CheckSessionParams, {} as LoginParams, asyncVoidFunc);
        const context = {operationId: 'fake'} as LoginRunContext;
        await job.kill(context);
        await expect(job.execute(context)).rejects.toThrow(new ScraperLibError(errorType.JOB_STOPPED_BY_USER));
    });

    describe('Invalid session', () => {
        afterEach(jest.restoreAllMocks); //TODO is this needed here?
        function getBinaryProxyMock() {
            return {
                run: jest.fn().mockImplementation((command: Command) => {
                    if (command === Command.CHECK_SESSION) {
                        return Promise.resolve({result: Promise.reject({hasScrapeBlockingIssues: true, id: 'test'})});
                    } else if (command === Command.LOGIN) {
                        return Promise.resolve({result: Promise.resolve()});
                    }
                    return Promise.resolve({result: Promise.resolve()});
                })
            } as unknown as BinaryProxy;
        }

        const checkSessionParams = {} as CheckSessionParams;
        const loginParams = {credentials: {username: 'username', password: 'password'}} as unknown as LoginParams;

        test('execute tries to log in again in case of invalid session', async () => {
            // Create a specific mock for this test
            const specificBinaryProxyMock = getBinaryProxyMock();

            const job = new EnsureValidSessionJob(specificBinaryProxyMock, checkSessionParams, loginParams, asyncVoidFunc);
            const context = {operationId: 'fake', userId: 'o-U53r1D'} as LoginRunContext;
            await job.execute(context);
            expect(specificBinaryProxyMock.run).toHaveBeenCalledWith(Command.CHECK_SESSION, checkSessionParams, context.operationId, context.userId);
            expect(specificBinaryProxyMock.run).toHaveBeenCalledWith(Command.LOGIN, loginParams, context.operationId, context.userId);
        });

        test('execute throws SESSION_EXPIRED error if in case of invalid session and missing credentials (manual session)', async () => {
            loginParams.credentials = undefined;
            const updateStatus = jest.fn();
            const job = new EnsureValidSessionJob(getBinaryProxyMock(), checkSessionParams, loginParams, updateStatus);
            const context = {operationId: 'fake'} as LoginRunContext;
            await expect(job.execute(context)).rejects.toThrow(new ScraperLibError(errorType.SESSION_EXPIRED));
        });
    });
});

import {promises as fs} from 'fs';
import {resolve} from 'path';
import {EntryFactory} from '../../../processes/types/EntryFactory';
import {HttpScraperServiceClient} from '../../../telemetry/scraperService/httpScraperServiceClient';
import {Outbox} from '../../../telemetry/scraperService/outbox';
import {Source} from '../../../types';

describe('outputs', () => {
    let s2Client: HttpScraperServiceClient;

    beforeEach(() => {
        s2Client = new HttpScraperServiceClient('localhost', 'fake-token', 'fake-userId', 'fake-origin', new Outbox('fake/file/path'));
    });

    afterEach(async () => {
        await s2Client.close();
    });

    const testDirectory = resolve('./src/__tests__/integration/binaryOutputs');

    const files = ['example-output-js.ndjson', 'example-output-py.ndjson'];

    test.each(files)(`is valid for %s`, async (fileName) => {
        const filePath = resolve(testDirectory, fileName);
        const source = Source.APP_STORE_SALES;

        const fileContent = await fs.readFile(filePath, 'utf8');
        const lines = fileContent.split('\n');

        for (const [lineIndex, line] of lines.entries()) {
            if (!line) continue;

            try {
                const jsonEntry = {
                    source,
                    userId: 'o-U53r1D',
                    ...JSON.parse(line)
                };
                await EntryFactory.create(jsonEntry);
            } catch (error) {
                throw new Error(
                    `Error on line ${filePath}:${lineIndex + 1}: ${line}\n\n` +
                        `Formatted: ${JSON.stringify({source, ...JSON.parse(line)}, null, 2)}\n\n` +
                        `${error.message}`
                );
            }
        }
    });
});

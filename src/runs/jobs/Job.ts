import {ScraperConfiguration} from '../../configurations/ScraperConfiguration';
import {BinaryResult} from '../../processes/types';
import {RetryActionOptions} from '../../utils/retryAction';
import {BaseContext} from './context';

export type LoginResult = ScraperConfiguration[];

//TODO and extend it in this enum and boom, circular dependency
export type JobResult = BinaryResult | LoginResult | void;

export abstract class Job<Result extends JobResult = any, Context extends BaseContext = BaseContext> {
    dependencies: Job<Result>[] = [];
    retryOptions: Omit<RetryActionOptions<any>, 'target'> = {maxAttempts: 1};

    abstract execute(context: Context): Promise<Result>;

    abstract onFail(error: Error, context: Context): Promise<void>;

    abstract onSuccess(context: Context): Promise<void>;

    abstract kill(context: Context): Promise<void>;
}

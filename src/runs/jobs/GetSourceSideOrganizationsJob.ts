import {BinaryProxy, CommonParams} from '../../dependencies/BinaryProxy';
import {SourceSideOrganization} from '../../processes/types';
import {Execution} from '../../processes/types/Execution';
import {Command} from '../../types';
import {BaseContext} from './context';
import {Job} from './Job';

export class GetSourceSideOrganizationsJob extends Job<SourceSideOrganization[]> {
    constructor(private binaryProxy: BinaryProxy, private runParams: CommonParams) {
        super();
    }
    private execution?: Execution<SourceSideOrganization[]>;

    async execute(context: BaseContext): Promise<SourceSideOrganization[]> {
        const execution = await this.binaryProxy.run<SourceSideOrganization[]>(
            Command.GET_SOURCE_SIDE_ORGANIZATIONS,
            this.runParams,
            context.operationId,
            context.userId
        );
        return execution.result;
    }
    async onSuccess(): Promise<void> {
        // Handle success
    }

    async onFail(): Promise<void> {
        // Handle failure
    }

    async kill(context: BaseContext): Promise<void> {
        await this.execution?.kill(context.operationId);
    }
}

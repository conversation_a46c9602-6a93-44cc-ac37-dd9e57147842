import {errorType} from '../../configurations/errorType';
import {ScraperConfiguration} from '../../configurations/ScraperConfiguration';
import {ScraperConfigurationHandlerWizardUtil} from '../../configurations/ScraperConfigurationHandlerWizardUtil';
import {ScraperLibError} from '../../processes/types/errors';
import {LoginRunContext} from './context';
import {Job} from './Job';

export class SaveConfigurationToStorageJob extends Job<ScraperConfiguration[]> {
    constructor(private readonly scraperConfigurationHandlerWizardUtil: ScraperConfigurationHandlerWizardUtil) {
        super();
    }

    async execute(context: LoginRunContext): Promise<ScraperConfiguration[]> {
        if (!context.mutable.checkSessionResult || !context.mutable.checkSessionResult.id) {
            throw new ScraperLibError(errorType.INTERNAL_SCRAPER_LIB_ERROR, 'error', {
                message: 'CheckSessionResult is not defined and a configuration is trying to be saved',
                context
            });
        }

        return await this.scraperConfigurationHandlerWizardUtil.addSourceAccountAndHandleRelatedConfigs(context.source, {
            sessionPath: context.sessionPath,
            accountIdentifier: context.mutable.checkSessionResult!.id
        });
    }

    async onSuccess(): Promise<void> {
        // Handle success
    }

    async onFail(_error: Error): Promise<void> {
        //TODO remove the session file if created ?? Why? The session file might be used by something else
        // Handle failure
    }

    async kill(): Promise<void> {
        //TODO remove the session file if created ?? Why? The session file might be used by something else
        // Kill the job
    }
}

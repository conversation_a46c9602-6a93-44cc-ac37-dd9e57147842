import {EncryptedJsonFile} from '../../utils/EncryptedJsonFile';
import {removeFileOrDirectory} from '../../utils/fileUtils';
import {LoginWithCookiesRunContext} from './context';
import {Job} from './Job';

export class LoadCookiesToSessionJob extends Job<void> {
    async execute(context: LoginWithCookiesRunContext): Promise<void> {
        const encryptedJsonFile = new EncryptedJsonFile(context.sessionPath);
        await encryptedJsonFile.save({cookies: context.cookies});
    }

    async kill(_context: LoginWithCookiesRunContext): Promise<void> {
        return removeFileOrDirectory(_context.sessionPath, false);
    }

    onFail(_error: Error, _context: LoginWithCookiesRunContext): Promise<void> {
        return removeFileOrDirectory(_context.sessionPath, false);
    }

    onSuccess(_context: LoginWithCookiesRunContext): Promise<void> {
        return Promise.resolve(undefined);
    }
}

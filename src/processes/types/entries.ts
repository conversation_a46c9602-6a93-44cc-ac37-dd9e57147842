import {Equals, <PERSON>D<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Is<PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>idateIf} from 'class-validator';
import {ErrorType} from '../../configurations/errorType';
import {S2BaseEvent} from '../../telemetry/scraperService/scraperServiceEvents';
import {Source} from '../../types/Source';
import {BinaryResult} from './resultTypes';

//TODO rename once log levels are cleared up
export enum ScraperServiceLogLevel {
    INFO = 1,
    WARN = 2,
    ERROR = 3
}

type AllowedDataType = Record<string, unknown> | Array<unknown> | object | null | undefined;

/**
 * @deprecated Will be replaced with BaseEntry
 */
abstract class DeprecatedEntry {
    @IsDateString()
    timestamp: string;

    @IsNumber()
    logLevel: ScraperServiceLogLevel;

    source: Source;
    originId: string;
    authenticatedUserId: string;

    //TODO add initialization of these
    organizationId?: string;
    userId?: string;
}

/**
 * @deprecated Will be replaced with OutputEntry
 */
export class DeprecatedOutputEntry extends DeprecatedEntry {
    @IsNumber()
    @ValidateIf((_object, value) => value !== undefined)
    @Min(0)
    @Max(100)
    progress?: number;

    @Equals('output')
    type: 'output';

    @IsString()
    message: string;

    public static isOutputEntry(rawJson: object) {
        return rawJson['type'] === 'output';
    }
}

/**
 * @deprecated Will be replaced with ResultEntry
 */
export class DeprecatedResultEntry<T extends BinaryResult = BinaryResult> extends DeprecatedEntry {
    @IsDateString()
    timestamp: string;

    @Equals('result')
    type: 'result';

    @ValidateIf((_object, value) => value !== undefined)
    data: T;

    public static isResultEntry(rawJson: object) {
        return rawJson['type'] === 'result';
    }
}

/**
 * @deprecated Will be replaced with ErrorEntry
 */
export class DeprecatedErrorEntry extends DeprecatedEntry {
    @IsDateString()
    timestamp: string;

    @Equals('error')
    type: 'error';

    @ValidateIf((_object, value) => value !== undefined)
    data: string;
    errorType: ErrorType;

    public static isErrorEntry(rawJson: object) {
        return rawJson['type'] === 'error';
    }
}

/**
 * @deprecated Will be replaced with DualAuthEntry
 */
export class DeprecatedDualAuthEntry extends DeprecatedEntry {
    @IsDateString()
    timestamp: string;

    @Equals('dualAuth')
    type: 'dualAuth';

    @IsEnum(Source)
    source: Source;

    public static isDualAuthEntry(rawJson: object) {
        return rawJson['type'] === 'dualAuth';
    }
}

/**
 * @deprecated Will be replaced with TraceEntry
 */
export class DeprecatedTraceEntry extends DeprecatedEntry {
    @IsDateString()
    timestamp: string;

    @Equals('trace')
    type: 'trace';

    @IsString()
    message: string;

    public static isTraceEntry(rawJson: object) {
        return rawJson['type'] === 'trace';
    }
}

/**
 * @deprecated Will be replaced with EventEntry
 */
export class DeprecatedEventEntry extends DeprecatedEntry {
    @Equals('event')
    type: 'event';

    @IsString()
    message: string;

    data: AllowedDataType;

    public static isEventEntry(rawJson: object) {
        return rawJson['type'] === 'event';
    }

    public static toS2BaseEvent(jsonInput: DeprecatedEventEntry, operationId: string): S2BaseEvent {
        return {
            origin: jsonInput.originId,
            user_id: jsonInput.authenticatedUserId,
            client_timestamp: new Date(jsonInput.timestamp),
            operation_id: operationId,
            event_type: jsonInput.type, //TODO This will be a tricky one since it is not know and it is validated in s2
            body: {
                logLevel: jsonInput.logLevel,
                source: jsonInput.source,
                message: jsonInput.message,
                data: jsonInput.data
            }
        };
    }
}

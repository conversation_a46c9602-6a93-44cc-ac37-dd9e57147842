import {IsDateString, IsEnum, IsN<PERSON>ber, IsOptional, IsString, ValidateIf, ValidateNested, validate} from 'class-validator';
import {ScraperServiceLogLevel} from '../../../telemetry/scraperService/iScraperServiceClient';
import {Command, Source} from '../../../types';
import {Deferred} from '../../../utils/Deferred';
import {Execution} from '../Execution';

export interface EntryContext {
    command: Command;
    operationId: string;
    childProcessPid?: number;
    execution: Execution<any>;
    deferredResult: Deferred<any>;
}

export type EntryType = 'output' | 'result' | 'error' | 'dualAuth' | 'trace';

export class EntryValidationError extends Error {
    constructor(public readonly errors: string[]) {
        super(`Validation failed: ${errors.join(', ')}`);
    }
}

class MetadataSubEntry {
    @IsString()
    function_name: string;

    @IsNumber()
    line_no: number;

    @IsString()
    logger_name: string;

    @IsString()
    level_name: string;

    constructor(protected readonly json: Record<string, any>) {
        if (json) {
            Object.assign(this, json);
        }
    }
}

export abstract class BaseEntry<T extends EntryType> {
    @IsDateString()
    timestamp: string;

    @IsNumber()
    @IsEnum(ScraperServiceLogLevel)
    logLevel: ScraperServiceLogLevel = ScraperServiceLogLevel.INFO;

    @IsEnum(Source)
    source: Source;

    @IsString()
    originId: string;

    @ValidateIf((_object, value) => value !== undefined)
    @IsString()
    @IsOptional() // Currently it is set to "N/A" in scrapers-py and "not implemented" in scrapers-js, but some logs in scrapers-py like 'Sentry is disabled' do not sent this field
    authenticatedUserId: string | undefined;

    @IsString()
    userId: string;

    @IsNumber()
    version: string;

    type: T;

    @IsOptional()
    @ValidateNested()
    metadata?: MetadataSubEntry;

    static async create<T extends EntryType>(json: Record<string, any> & {type: T}): Promise<BaseEntry<T>> {
        // @ts-expect-error: Protected constructor issues
        const instance = new this(json);
        await instance.validate();
        return instance;
    }

    /*
     * This constructor is protected to prevent direct instantiation of the class.
     * Use the static create method to create an instance of the class.
     */
    protected constructor(protected readonly json: Record<string, any> & {type: T}) {
        if (json) {
            Object.assign(this, json);

            if (json.metadata) {
                this.metadata = new MetadataSubEntry(json.metadata);
            }
        }
    }

    async validate() {
        const errors = await validate(this);
        if (errors.length > 0) {
            const errorStrings = errors.map((e) => e.toString());
            throw new EntryValidationError(errorStrings);
        }
    }

    abstract handle(context: EntryContext): Promise<void>;
}

export class BaseSubEntry {
    constructor(protected readonly json: Record<string, any>) {
        if (json) {
            Object.assign(this, json);
        }
    }
}

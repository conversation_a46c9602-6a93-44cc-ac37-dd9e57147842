import {v4 as uuidv4} from 'uuid';
import {Storage} from '../storage/Storage';
import {iScraperServiceClient} from '../telemetry/scraperService/iScraperServiceClient';
import {TriggeredBy} from '../telemetry/scraperService/scraperServiceEvents';
import * as telemetry from '../telemetry/telemetry';
import {Source} from '../types';
import {removeFileOrDirectory} from '../utils/fileUtils';
import {getAvailableSources, getRelatedSources} from '../utils/sourceUtils';
import {ScraperConfiguration, ScraperConfigurationAddParams, ScraperConfigurationEditParams, ScraperConfigurationStatus} from './ScraperConfiguration';
import {SourceAccount, SourceAccountAddParams, SourceAccountEditParams} from './SourceAccount';

export class ScraperConfigurationHandlerWizardUtil {
    constructor(private readonly storage: Storage, private readonly scraperServiceClient: iScraperServiceClient) {}

    public async getScraperConfigurations(): Promise<ScraperConfiguration[]> {
        const configs = (await this.createMissingScraperConfigurationsBasedOnRelatedSources()) || [];
        return configs.filter((config): config is ScraperConfiguration => !!config);
    }

    public async getScraperConfiguration(source: Source): Promise<ScraperConfiguration | undefined> {
        telemetry.trace(`Getting scraper configuration for source ${source}`);
        return this.storage.getScraperConfigurationBySource(source);
    }

    public async addSourceAccountAndHandleRelatedConfigs(source: Source, sourceAccount: SourceAccountAddParams): Promise<ScraperConfiguration[]> {
        const {id: sourceAccountId} = await this.storage.addSourceAccount(sourceAccount);
        const changedConfigs: ScraperConfiguration[] = [];
        for (const relatedSource of getRelatedSources(source)) {
            // TODO to zwraca samo source o które zapytano
            const config = await this.getScraperConfiguration(relatedSource);
            if (!config) {
                changedConfigs.push(
                    await this.storage.addScraperConfiguration({
                        source: relatedSource,
                        sourceAccountId
                    })
                );
            } else {
                changedConfigs.push(
                    await this.storage.editScraperConfiguration({
                        source: relatedSource,
                        sourceAccountId
                    })
                );
            }
        }
        await this.removeAllUnusedSourceAccounts();
        return changedConfigs;
    }

    public async removeAllUnusedSourceAccounts() {
        telemetry.trace(`Removing all unused source accounts`);
        const configs = await this.storage.listScraperConfigurations();
        const accounts = await this.storage.listSourceAccounts();

        const unusedAccounts = accounts.filter((account) => !configs.some((config) => config.sourceAccountId === account.id));
        await Promise.all(unusedAccounts.map((unusedAccount) => this.deleteSourceAccount(unusedAccount.id)));
    }

    public async createMissingScraperConfigurationsBasedOnRelatedSources(): Promise<Array<ScraperConfiguration | undefined>> {
        return Promise.all(getAvailableSources().map(async (source: Source) => await this.storage.getScraperConfigurationBySource(source, true)));
    }

    public async deleteSourceAccount(sourceAccountId: string): Promise<void> {
        telemetry.trace(`Deleting source account ${sourceAccountId}`);
        const sourceAccount = await this.storage.getSourceAccount(sourceAccountId);
        if (sourceAccount) {
            telemetry.trace(`Deleting source account session file ${sourceAccount.sessionPath}`);
            await removeFileOrDirectory(sourceAccount.sessionPath);
        }
        return this.storage.deleteSourceAccount(sourceAccountId);
    }

    public async disableScraperConfiguration(source: Source): Promise<ScraperConfiguration> {
        telemetry.trace(`Disabling scraper configuration for source: ${source}`);
        const configuration = await this.storage.editScraperConfiguration({
            source,
            status: ScraperConfigurationStatus.DISABLED
        });
        this.scraperServiceClient.scheduleScraperStateChangedEvent({
            //TODO this could be a job ... I guess?
            source,
            triggeredBy: TriggeredBy.USER_VIA_ELECTRON,
            newState: 'DISABLED',
            operationId: uuidv4()
        });
        return configuration;
    }

    public async addScraperConfiguration(config: ScraperConfigurationAddParams, sourceAccount?: SourceAccountAddParams): Promise<ScraperConfiguration> {
        return this.storage.addScraperConfiguration(config, sourceAccount);
    }

    public async editScraperConfiguration(scraperConfiguration: ScraperConfigurationEditParams): Promise<ScraperConfiguration> {
        return this.storage.editScraperConfiguration(scraperConfiguration);
    }

    public async addSourceAccount(sourceAccount: SourceAccountAddParams): Promise<SourceAccount> {
        return this.storage.addSourceAccount(sourceAccount);
    }

    getSourceAccount(sourceAccountId: string) {
        return this.storage.getSourceAccount(sourceAccountId);
    }

    async getScraperConfigurationBySource(
        source: Source,
        shouldCreateBasedOnRelatedSources?: boolean,
        saveConfiguration?: boolean
    ): Promise<ScraperConfiguration | undefined> {
        return this.storage.getScraperConfigurationBySource(source, shouldCreateBasedOnRelatedSources, saveConfiguration);
    }

    async editSourceAccount(sourceAccount: SourceAccountEditParams): Promise<SourceAccount> {
        return this.storage.editSourceAccount(sourceAccount);
    }

    async deleteScraperConfiguration(source: Source): Promise<void> {
        return this.storage.deleteScraperConfiguration(source);
    }

    async listSourceAccounts(): Promise<SourceAccount[]> {
        return this.storage.listSourceAccounts();
    }
}

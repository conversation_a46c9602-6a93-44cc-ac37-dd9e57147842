import {PublicScraperConfiguration} from '../publicInterfaces/PublicScraperConfiguration';
import {RunManager} from '../runs/RunManager';
import {ScrapeRun} from '../runs/ScrapeRun';
import {ensureJsonSerializable} from '../utils/serialization';
import {ScraperConfiguration, ScraperConfigurationStatus} from './ScraperConfiguration';

/**
 * Because of the use of instanceof ScrapeRun this method is renderer unsafe.
 * @param scraperConfiguration
 */
export function convertToPublicScraperConfiguration(config: ScraperConfiguration, runManager: RunManager): PublicScraperConfiguration {
    let activeRun: PublicScraperConfiguration['activeRun'] = undefined;

    const runningRun = runManager.getRunningRunForSource(config.source);
    const queuedRun = runManager.getScheduledRunForSource(config.source);

    const run = runningRun || queuedRun;

    if (run) {
        activeRun = {
            percentageProgress: run.progress,
            startDate: run.startDate
        };
        if (run instanceof ScrapeRun) {
            activeRun.numOfDataRangesToScrape = run.dateRanges?.length;
            activeRun.numberOfDaysToScrape = run.totalItems;
            activeRun.scrapedDays = run.processedItems;
        }
    }

    let status = config.status;
    if (runningRun) {
        status = ScraperConfigurationStatus.RUNNING_SCRAPE;
    } else if (queuedRun) {
        status = ScraperConfigurationStatus.SCHEDULED;
    }

    const result: PublicScraperConfiguration = {
        id: config.id,
        source: config.source,
        activeRun,
        lastSuccessfulScrapeDate: config.lastSuccessfulScrapeDate,
        status,
        errorType: config.errorType,
        previousStatus: config.previousStatus,
        previousErrorType: config.previousErrorType,
        sourceAccountId: config.sourceAccountId,
        createdAt: config.createdAt
    };

    return ensureJsonSerializable(result, 'Could not serialize configuration object');
}

import * as path from 'path';
import {getCliParams} from '../api/ScraperApi';
import {FeatureFlag} from '../featureFlags/featureFlags';
import {spawnProcess} from '../processes/spawnProcess';
import {BinaryLoginResult, BinaryResult, CheckSessionResult, ManualLoginDetailsResult, ScrapeResult, SourceSideOrganization} from '../processes/types';
import {Execution} from '../processes/types/Execution';
import {Credentials} from '../runs/credentials';
import * as telemetry from '../telemetry/telemetry';
import {Command, Source} from '../types';
import {ensureDirExists} from '../utils/fileUtils';
import {DependenciesManager} from './DependenciesManager';

const resultClass = {
    [Command.LOGIN]: BinaryLoginResult,
    [Command.CHECK_SESSION]: CheckSessionResult,
    [Command.GET_SOURCE_SIDE_ORGANIZATIONS]: SourceSideOrganization,
    [Command.GET_PRODUCTS]: null,
    [Command.SCRAPE]: ScrapeResult,
    [Command.GET_MANUAL_LOGIN_DETAILS]: ManualLoginDetailsResult,
    // Source specific overrides
    [Source.PLAYSTATION_SALES]: {
        [Command.GET_MANUAL_LOGIN_DETAILS]: null
    }
};

export type ExecutionResultClass = (new (...args: any[]) => any) | null;
export const getResultClass = (command: Command, source: Source): ExecutionResultClass =>
    resultClass[source]?.[command] !== undefined ? resultClass[source]?.[command] : resultClass[command];

export interface CommonParams {
    /**
     * Use this in case you want to run a specific binary version, not related to its default source configuration.
     * For example, when you want to run a shadowMode command
     */
    forcedScrapersExecPath?: string;

    /**
     * Use this in case you want to run a specific binary version, not related to its default source configuration.
     * For example, when you want to run a shadowMode command
     */
    forcedChromiumExecPath?: string;

    source: Source;
    /**
     * SessionPath can be undefined for stateless jobs like getManualLoginData.
     * But more often than not it is required.
     */
    sessionPath?: string;
    screenshotAndHtmlDumpPath?: string;
    featureFlags?: FeatureFlag[];
}

export interface ScrapeParams extends CommonParams {
    skuToIgnore?: string[];
    credentials?: Credentials;
    dateFrom: Date;
    dateTo: Date;
}

export type CheckSessionParams = CommonParams;
export type GetSourceSideOrganizationsParams = CommonParams;

export interface LoginParams extends CommonParams {
    credentials: Credentials | undefined;
}

export type BinaryParams = ScrapeParams | LoginParams | CheckSessionParams | GetSourceSideOrganizationsParams;

const parseDate = (date: Date): string => date.toISOString().split('T')[0];
const parseObjectToCliStandard = (input: Record<string, any>): string => JSON.stringify(input);

// We added source to prevent conflicts with manifest.json in main folder
export const buildReportPath = (mainDir: string, source: Source, fileName: string) => path.join(mainDir, 'reports', source, fileName);

export class BinaryProxy {
    constructor(private mainDir: string, private dependenciesManager: DependenciesManager) {
        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        // @ts-ignore
        void ensureDirExists(path.join(mainDir, 'sessions'));
    }

    async run<ResultType extends BinaryResult>(command: Command, params: BinaryParams, operationId: string, userId: string): Promise<Execution<ResultType>> {
        await ensureDirExists(path.join(this.mainDir, 'sessions'));
        await ensureDirExists(path.join(this.mainDir, 'reports', params.source));

        /**
         * this is a fix for a bug with multiple profiles (PPT/DPT stuff on VMs). Should be created in electron but it's not.
         * NOTE https://indiebi.atlassian.net/browse/DPT-803
         */
        if (params.screenshotAndHtmlDumpPath) {
            await ensureDirExists(params.screenshotAndHtmlDumpPath);
        }

        const {chromiumExecPath, scrapersExecPath} = await this.dependenciesManager.getDependencyExecPaths(params.source);

        const parsedParams = this.parseParams(command, params, params.forcedChromiumExecPath ?? chromiumExecPath);
        try {
            return await spawnProcess<ResultType>(params.forcedScrapersExecPath ?? scrapersExecPath, parsedParams, operationId, userId);
        } catch (error) {
            telemetry.logAndThrowError(
                error,
                {
                    command,
                    scrapersExecPath: params.forcedScrapersExecPath ?? scrapersExecPath,
                    chromiumExecPath: params.forcedChromiumExecPath ?? chromiumExecPath
                },
                true
            );
        }
    }

    private parseParams(command: Command, params: BinaryParams, chromiumExecPath: string): string[] {
        const apiParams = getCliParams();
        const {credentials, source, dateFrom, dateTo, skuToIgnore} = params as any;
        const paramsArray: string[] = [command]; // please don't change it. `command` always has to be first param
        paramsArray.push(`--source=${source}`);
        paramsArray.push(`--output=json`);
        paramsArray.push(`--apiUrl=${apiParams.apiUrl}`);
        paramsArray.push(`--apiToken=${apiParams.apiToken}`);
        paramsArray.push(`--chromePath='${chromiumExecPath}'`);
        if (params.sessionPath) {
            paramsArray.push(`--sessionFile='${params.sessionPath}'`);
        }
        if (credentials) {
            paramsArray.push(`--credentials=${parseObjectToCliStandard(credentials)}`);
        }
        if (command === Command.SCRAPE) {
            paramsArray.push(`--reportPath=${path.join(this.mainDir, 'reports', source)}`);
            if (dateFrom) {
                paramsArray.push(`--from=${parseDate(dateFrom)}`);
            }
            if (dateTo) {
                paramsArray.push(`--to=${parseDate(dateTo)}`);
            }
            if (skuToIgnore) {
                paramsArray.push(`--excludedSkus=${parseObjectToCliStandard(skuToIgnore)}`);
            }
        }
        if (process.env.DEBUG) {
            paramsArray.push('--headless=false');
        }
        if (params.screenshotAndHtmlDumpPath) {
            paramsArray.push(`--dumpDir=${params.screenshotAndHtmlDumpPath}`);
        }
        if (params.featureFlags?.length) {
            paramsArray.push(`--featureFlags=${parseObjectToCliStandard(params.featureFlags)}`);
        }
        return paramsArray;
    }
}

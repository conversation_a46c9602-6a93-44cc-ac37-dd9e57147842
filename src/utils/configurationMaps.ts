import {ScraperConfiguration, ScraperConfigurationStatus} from '../configurations/ScraperConfiguration';
import {RunManager} from '../runs/RunManager';

//TODO delete me later
export const configWithDynamicStatuses = (config: ScraperConfiguration, runManager: RunManager): ScraperConfiguration => {
    const runningRun = runManager.getRunningRunForSource(config.source);
    const queuedRun = runManager.getScheduledRunForSource(config.source);
    let status = config.status;
    if (runningRun) {
        status = ScraperConfigurationStatus.RUNNING_SCRAPE;
    } else if (queuedRun) {
        status = ScraperConfigurationStatus.SCHEDULED;
    }

    return {
        ...config,
        status
    };
};
